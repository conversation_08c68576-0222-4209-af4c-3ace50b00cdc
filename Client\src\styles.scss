html,
body {
  margin: 0;
  padding: 0;
  height: 100vh;
  width: 100vw;
  overflow-x: hidden;
  font-family: Helvetica, Arial, sans-serif;
  font-weight: 700;
  color: rgb(0, 0, 0);

  background: #ffffff;
}

.app-container {
  background-color: #f0f7ff;
  min-height: calc(100vh - 80px);
  padding: 20px;
  border-radius: 35px 35px 0 0;
}

// PrimeNG Dialog Customizations
.p-dialog {
  .p-dialog-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
  }

  .p-dialog-content {
    padding: 0;
  }

  .p-dialog-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid #e9ecef;
  }
}

// Event Reject Dialog Specific Styles
.event-reject-dialog {
  .p-dropdown-panel {
    width: 100%;
  }

  .p-dropdown-items-wrapper {
    max-height: 200px;
  }
}

// Confirmation Dialog Styling
// .p-confirmdialog {
//   .p-dialog {
//     border-radius: 12px !important;
//     box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15) !important;
//     border: none !important;
//     min-width: 400px !important;
//     max-width: 500px !important;
//   }

//   .p-dialog-header {
//     background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
//     color: white !important;
//     padding: 1.5rem 2rem !important;
//     border-radius: 12px 12px 0 0 !important;
//     border-bottom: none !important;

//     .p-dialog-title {
//       font-size: 1.25rem !important;
//       font-weight: 600 !important;
//       color: white !important;
//     }

//     .p-dialog-header-close {
//       color: white !important;
//       background: rgba(255, 255, 255, 0.1) !important;
//       border: 1px solid rgba(255, 255, 255, 0.2) !important;
//       border-radius: 6px !important;
//       width: 32px !important;
//       height: 32px !important;
//       transition: all 0.2s ease !important;

//       &:hover {
//         background: rgba(255, 255, 255, 0.2) !important;
//         border-color: rgba(255, 255, 255, 0.3) !important;
//       }

//       .p-dialog-header-close-icon {
//         color: white !important;
//         font-size: 1rem !important;
//       }
//     }
//   }

//   .p-dialog-content {
//     padding: 2rem !important;
//     background: white !important;
//     display: flex !important;
//     align-items: flex-start !important;
//     gap: 1rem !important;

//     .p-confirmdialog-icon {
//       color: #ffc107 !important;
//       font-size: 2.5rem !important;
//       margin-top: 0.25rem !important;
//       flex-shrink: 0 !important;
//     }

//     .p-confirmdialog-message {
//       font-size: 1rem !important;
//       line-height: 1.5 !important;
//       color: #495057 !important;
//       margin: 0 !important;
//       flex: 1 !important;
//     }
//   }

//   .p-dialog-footer {
//     padding: 1.5rem 2rem !important;
//     background: #f8f9fa !important;
//     border-radius: 0 0 12px 12px !important;
//     border-top: 1px solid #e9ecef !important;
//     display: flex !important;
//     justify-content: flex-end !important;
//     gap: 0.75rem !important;

//     .p-button {
//       padding: 0.75rem 1.5rem !important;
//       font-weight: 500 !important;
//       border-radius: 6px !important;
//       transition: all 0.2s ease !important;
//       min-width: 80px !important;
//       font-size: 0.95rem !important;

//       &.p-button-text {
//         background: transparent !important;
//         border: 1px solid #6c757d !important;
//         color: #6c757d !important;

//         &:hover {
//           background: #6c757d !important;
//           color: white !important;
//           border-color: #6c757d !important;
//         }
//       }

//       &.p-button-danger {
//         background: linear-gradient(
//           135deg,
//           #dc3545 0%,
//           #c82333 100%
//         ) !important;
//         border: 1px solid #dc3545 !important;
//         color: white !important;

//         &:hover {
//           background: linear-gradient(
//             135deg,
//             #c82333 0%,
//             #a71e2a 100%
//           ) !important;
//           border-color: #c82333 !important;
//           transform: translateY(-1px) !important;
//           box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3) !important;
//         }
//       }

//       &.p-button-success {
//         background: linear-gradient(
//           135deg,
//           #28a745 0%,
//           #20c997 100%
//         ) !important;
//         border: 1px solid #28a745 !important;
//         color: white !important;

//         &:hover {
//           background: linear-gradient(
//             135deg,
//             #20c997 0%,
//             #17a2b8 100%
//           ) !important;
//           border-color: #20c997 !important;
//           transform: translateY(-1px) !important;
//           box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3) !important;
//         }
//       }
//     }
//   }
// }

// Special styling for delete confirmation dialogs
.p-confirmdialog.delete-confirmation {
  .p-dialog-header {
    margin-bottom: 15px;
  }

  .p-confirmdialog-icon {
    margin-left: 12px;
  }
  // .p-confirmdialog-message {
  //   // font-size: 1.25rem !important;
  //   line-height: 1.5 !important;
  //   color: #495057 !important;
  //   margin: 8px !important;
  //   flex: 1 !important;
  // }
  .p-dialog-footer {
    margin-top: 15px;
  }
}

// Special styling for status toggle confirmation dialogs
.p-confirmdialog.status-confirmation {
  .p-dialog-header {
    margin-bottom: 15px;
  }
  .p-confirmdialog-message {
    // font-size: 1.25rem !important;
    line-height: 1.5 !important;
    color: #495057 !important;
    margin: 8px !important;
    flex: 1 !important;
  }
  .p-confirmdialog-icon {
    margin-left: 8px !important;
  }
  .p-dialog-footer {
    margin-top: 15px;
  }
}

// Dialog mask overlay styling
.p-dialog-mask {
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(3px) !important;
  animation: fadeIn 0.3s ease-out !important;
}

// Dialog animation
.p-dialog {
  animation: slideInDown 0.3s ease-out !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

// Responsive adjustments for confirmation dialogs
@media (max-width: 576px) {
  .p-confirmdialog {
    .p-dialog {
      min-width: 320px !important;
      max-width: 90vw !important;
      margin: 1rem !important;
    }

    .p-dialog-header {
      padding: 1rem 1.5rem !important;

      .p-dialog-title {
        font-size: 1.1rem !important;
      }
    }

    .p-dialog-content {
      padding: 1.5rem !important;

      .p-confirmdialog-icon {
        font-size: 2rem !important;
      }

      .p-confirmdialog-message {
        font-size: 0.95rem !important;
      }
    }

    .p-dialog-footer {
      padding: 1rem 1.5rem !important;
      flex-direction: column !important;

      .p-button {
        width: 100% !important;
        margin-bottom: 0.5rem !important;

        &:last-child {
          margin-bottom: 0 !important;
        }
      }
    }
  }
}

// Global Pagination Styles
// For Bootstrap pagination
.pagination {
  .page-link {
    color: #dc3545;
    border-color: #dee2e6;
    padding: 0.375rem 0.75rem;

    &:hover {
      background-color: #f8f9fa;
      border-color: #dee2e6;
      color: #dc3545;
    }
  }

  .page-item.active .page-link {
    background-color: #dc3545;
    border-color: #dc3545;
    color: #fff;
  }
}

// For PrimeNG pagination
:root {
  --pagination-color: #dc3545;
}

// Global PrimeNG paginator styling
:host ::ng-deep .p-paginator,
::ng-deep .p-paginator {
  .p-paginator-page.p-highlight {
    background-color: #dc3545 !important;
    color: white !important;
    border-color: #dc3545 !important;
  }

  .p-paginator-page:not(.p-highlight):hover,
  .p-paginator-next:hover,
  .p-paginator-prev:hover,
  .p-paginator-first:hover,
  .p-paginator-last:hover {
    color: #dc3545 !important;
  }

  .p-paginator-page,
  .p-paginator-next,
  .p-paginator-prev,
  .p-paginator-first,
  .p-paginator-last {
    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
  }
}

// Global PrimeNG form components green focus styling
::ng-deep {
  // Input Text focus styling
  .p-inputtext:enabled:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // Textarea focus styling
  .p-textarea:enabled:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // Dropdown focus styling
  .p-dropdown:not(.p-disabled).p-focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // Calendar focus styling
  .p-calendar:not(.p-disabled).p-focus .p-inputtext {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // File Upload focus styling
  .p-fileupload .p-fileupload-choose:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // Checkbox focus styling
  .p-checkbox .p-checkbox-box:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // Radio Button focus styling
  .p-radiobutton .p-radiobutton-box:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
    outline: none !important;
  }

  // Remove red focus states from invalid fields when not in error state
  .p-inputtext:enabled:focus:not(.ng-invalid.ng-dirty),
  .p-textarea:enabled:focus:not(.ng-invalid.ng-dirty),
  .p-dropdown:not(.p-disabled).p-focus:not(.ng-invalid.ng-dirty),
  .p-calendar:not(.p-disabled).p-focus .p-inputtext:not(.ng-invalid.ng-dirty) {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
  }

  // Keep red styling for actual validation errors
  .p-inputtext.ng-invalid.ng-dirty,
  .p-textarea.ng-invalid.ng-dirty,
  .p-dropdown.ng-invalid.ng-dirty,
  .p-calendar.ng-invalid.ng-dirty .p-inputtext {
    border-color: #dc3545 !important;
  }
}

// Global Back Button Styles
.back-button {
  display: flex;
  align-items: center;
  color: #0d6efd !important;
  text-decoration: none;
  font-weight: 500;
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;

  i,
  .pi,
  .bi {
    margin-top: 3px;
    margin-right: 0.5rem;
    font-size: 1.2rem;
    color: #0d6efd !important; // Blue color for icons
  }

  span {
    font-size: 1rem;
    color: #212529; // Dark color for text
  }
  h2 {
    color: #212529; // Dark color for text
  }

  &:hover {
    text-decoration: none;

    i,
    .pi,
    .bi {
      color: #0a58ca !important; // Darker blue on hover
    }
  }
}

// Global Standardized Button Styles
// These styles ensure consistency across all PrimeNG buttons in the application
.p-button {
  border-radius: 4px !important;
  padding: 0.75rem 1.25rem !important;
  height: auto !important;
  min-width: 100px !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

// Filter buttons (smaller size for list components)
.filter-btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 38px !important;
}

// Outlined danger buttons (Cancel, Clear, etc.)
.p-button-outlined.p-button-danger {
  background-color: white !important;
  color: #dc3545 !important;
  border: 1px solid #dc3545 !important;

  &:hover {
    background-color: rgba(220, 53, 69, 0.04) !important;
    border-color: #c82333 !important;
    color: #c82333 !important;
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
  }

  &:active {
    background-color: rgba(220, 53, 69, 0.08) !important;
    border-color: #c82333 !important;
    color: #c82333 !important;
  }
}

// Solid danger buttons (Primary actions, Save, etc.)
.p-button-danger:not(.p-button-outlined) {
  background-color: #dc3545 !important;
  color: white !important;
  border: 1px solid #dc3545 !important;

  &:hover {
    background-color: #c82333 !important;
    border-color: #c82333 !important;
  }

  &:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
  }

  &:active {
    background-color: #bd2130 !important;
    border-color: #bd2130 !important;
  }

  &:disabled {
    background-color: #e9a8ae !important;
    border-color: #e9a8ae !important;
    cursor: not-allowed !important;
    opacity: 0.65 !important;
  }
}

// Small buttons
.p-button-sm {
  padding: 0.375rem 0.75rem !important;
  font-size: 0.875rem !important;
  min-width: 80px !important;
}

// Icon spacing in buttons
.p-button i + span,
.p-button .bi + span,
.p-button .pi + span {
  margin-left: 0.25rem !important;
}

.p-button span + i,
.p-button span + .bi,
.p-button span + .pi {
  margin-left: 0.25rem !important;
}
