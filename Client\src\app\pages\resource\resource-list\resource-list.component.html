<div class="container mt-4">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2>Resource</h2>

    <div class="d-flex">
      <button
        pButton
        class="p-button-outlined p-button-danger me-2 filter-btn"
        (click)="toggleFilters()"
      >
        <i class="pi pi-filter me-1"></i>
        {{ showFilters ? "Hide" : "Filters" }}
      </button>

      <button
        pButton
        class="p-button-outlined p-button-danger me-2 filter-btn clear-btn"
        (click)="clearFilters()"
        *ngIf="showFilters"
      >
        <i class="pi pi-filter-slash me-1"></i>
        Clear
      </button>

      <button
        pButton
        class="p-button-danger filter-btn"
        (click)="addResource()"
      >
        <i class="bi bi-plus-lg me-1"></i>
        Add Resource
      </button>
    </div>
  </div>

  <!-- Filter Panel -->
  <div class="card mb-3" *ngIf="showFilters">
    <div class="card-body py-2 mb-4">
      <form [formGroup]="filterForm">
        <div class="row g-3">
          <!-- Organizational Title -->
          <div class="col-md-4">
            <label class="form-label small mb-1">Organizational Title</label>
            <div class="p-input-icon-left w-100">
              <i class="pi pi-search"></i>
              <input
                type="text"
                pInputText
                class="w-100"
                formControlName="organizationalTitle"
                placeholder="Filter by title"
              />
            </div>
          </div>

          <!-- Category -->
          <div class="col-md-4">
            <label class="form-label small mb-1">Category</label>
            <p-dropdown
              [options]="resourceCategories"
              formControlName="category"
              placeholder="Select a category"
              [showClear]="true"
              styleClass="w-100"
              optionLabel="name"
              optionValue="value"
            ></p-dropdown>
          </div>

          <!-- Resource Type -->
          <div class="col-md-4">
            <label class="form-label small mb-1">Resource Type</label>
            <p-dropdown
              [options]="resourceTypes"
              formControlName="resourceType"
              placeholder="Select resource type"
              [showClear]="true"
              styleClass="w-100"
              optionLabel="name"
              optionValue="value"
            ></p-dropdown>
          </div>
        </div>
      </form>
    </div>
  </div>

  <p-toast position="top-right"></p-toast>
  <p-confirmDialog
    key="deleteResource"
    styleClass="delete-confirmation"
    [style]="{ width: '450px' }"
    [baseZIndex]="10000"
    [autoZIndex]="true"
  >
  </p-confirmDialog>

  <div class="table-container border rounded-3 overflow-hidden bg-white">
    <div class="table-responsive">
      <table class="table custom-row-lines table-hover mb-0">
        <thead class="table-head text-white sticky-top">
          <tr>
            <th>Logo</th>
            <th
              class="sortable-header"
              (click)="sortResources('organizationTitle')"
              style="cursor: pointer"
            >
              Organization Title
              <i class="bi" [ngClass]="getSortIcon('organizationTitle')"></i>
            </th>
            <th
              class="sortable-header"
              (click)="sortResources('resourceCategory')"
              style="cursor: pointer"
            >
              Category
              <i class="bi" [ngClass]="getSortIcon('resourceCategory')"></i>
            </th>
            <th
              class="sortable-header"
              (click)="sortResources('type')"
              style="cursor: pointer"
            >
              Resource Type
              <i class="bi" [ngClass]="getSortIcon('type')"></i>
            </th>
            <th
              class="sortable-header"
              (click)="sortResources('createdAt')"
              style="cursor: pointer"
            >
              Created on
              <i class="bi" [ngClass]="getSortIcon('createdAt')"></i>
            </th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngIf="isLoading" class="text-center">
            <td colspan="7">
              <p-progress-spinner
                strokeWidth="8"
                fill="transparent"
                animationDuration=".5s"
                [style]="{ width: '25px', height: '35px', color: '#007bff' }"
              />
            </td>
          </tr>
          <tr *ngIf="!isLoading && error" class="text-center">
            <td colspan="7">
              <div class="alert alert-danger mb-0">
                {{ error }}
                <button
                  class="btn btn-link text-danger"
                  (click)="loadResources()"
                >
                  <i class="bi bi-arrow-clockwise"></i> Retry
                </button>
              </div>
            </td>
          </tr>
          <tr
            *ngIf="!isLoading && !error && resources.length === 0"
            class="text-center"
          >
            <td colspan="7">No resources found</td>
          </tr>
          <tr *ngFor="let resource of resources">
            <td>
              <img
                [src]="getFullImagePath(resource.resourceLogoUrl)"
                alt="Resource Logo"
                class="img-fluid rounded-circle"
                style="width: 50px; height: 50px; object-fit: cover"
              />
            </td>
            <td class="my-2">{{ resource.organizationTitle }}</td>
            <td>
              {{ formatResourceCategory(resource.resourceCategory || "") }}
            </td>
            <td>{{ formatResourceType(resource.resourceTypeName || "") }}</td>
            <td>{{ resource.createdAt | date: "mediumDate" }}</td>

            <td>
              <button
                class="btn btn-link text-secondary me-2 p-0"
                (click)="viewResourceDetails(resource.id)"
                title="View Details"
              >
                <i class="bi bi-eye"></i>
              </button>
              <button
                class="btn btn-link text-secondary me-2 p-0"
                (click)="editResource(resource.id)"
                title="Edit Resource"
              >
                <i class="bi bi-pencil"></i>
              </button>
              <button
                class="btn btn-link p-0"
                (click)="deleteResource(resource.id)"
                title="Delete Resource"
              >
                <i class="bi bi-trash"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="d-flex justify-content-between align-items-center mt-3 mb-5">
    <!-- Entries info text -->
    <div class="text-muted">
      Showing {{ (currentPage - 1) * pageSize + 1 }} to
      {{ Math.min(currentPage * pageSize, totalItems) }} of
      {{ totalItems }} entries
    </div>

    <!-- Pagination -->
    <nav aria-label="Page navigation" *ngIf="totalPages > 1">
      <ul class="pagination mb-0">
        <li class="page-item" [class.disabled]="currentPage === 1">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage - 1)"
            tabindex="-1"
          >
            Previous
          </a>
        </li>

        <li
          class="page-item"
          *ngFor="let page of pages"
          [class.active]="page === currentPage"
        >
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(page)"
          >
            {{ page }}
          </a>
        </li>

        <li class="page-item" [class.disabled]="currentPage === totalPages">
          <a
            class="page-link"
            href="javascript:void(0)"
            (click)="onPageChange(currentPage + 1)"
          >
            Next
          </a>
        </li>
      </ul>
    </nav>
  </div>
</div>
