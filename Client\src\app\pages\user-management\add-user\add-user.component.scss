/* Add proper spacing for the form container */
.container {
  max-width: 1000px;
}

// Loading overlay styles
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

/* Header container with proper spacing */
.header-container {
  padding: 12px 0;
  // margin-bottom: 20px !important;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Card styling */
.card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: white;
}

/* Form controls */
input,
textarea,
p-dropdown {
  border-radius: 4px;
}

input::placeholder,
textarea::placeholder {
  color: #adb5bd;
}

/* Label styling */
.form-label {
  font-weight: 500;
  color: #495057;
}

/* Section headings */
h5,
h6 {
  font-weight: 600;
  color: #212529;
}

/* Error summary styling */
.alert-danger {
  background-color: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.alert-danger ul {
  margin-bottom: 0;
}

.alert-danger li {
  margin-bottom: 4px;
}

.alert-danger li:last-child {
  margin-bottom: 0;
}

/* Button styling */
button.p-button {
  border-radius: 4px;
  padding: 0.75rem 1.25rem !important;
  height: auto !important;
  min-width: 100px;
}

.cancel-btn {
  background-color: white !important;
  color: #dc3545 !important;
  border: 1px solid #dc3545 !important;
}

.save-btn {
  background-color: #dc3545 !important;
  color: white !important;
  border: 1px solid #dc3545 !important;
}

:host ::ng-deep .p-button {
  margin: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Dropdown styling */
.dropdown-field {
  width: 100% !important;
}

:host ::ng-deep .p-dropdown {
  width: 100% !important;
}

/* Description textarea */
textarea {
  width: 100% !important;
  min-height: 100px;
  resize: vertical;
}

// Component-specific compact form overrides to ensure styling takes effect
:host ::ng-deep {
  .compact-form {
    // Force PrimeNG input text styling
    .p-inputtext {
      height: 28px !important;
      padding: 2px 8px !important;
      font-size: 0.875rem !important;
      line-height: 1.4 !important;
      border: 1px solid #ced4da !important;
      border-radius: 0.25rem !important;
    }

    // Force PrimeNG textarea styling
    .p-textarea {
      min-height: 28px !important;
      padding: 2px 8px !important;
      font-size: 0.875rem !important;
      line-height: 1.4 !important;
      border: 1px solid #ced4da !important;
      border-radius: 0.25rem !important;
    }

    // Force PrimeNG dropdown styling
    .p-dropdown {
      height: 28px !important;
      border: 1px solid #ced4da !important;
      border-radius: 0.25rem !important;

      .p-dropdown-label {
        padding: 2px 8px !important;
        line-height: 1.4 !important;
        font-size: 0.875rem !important;
        height: 24px !important;
        display: flex !important;
        align-items: center !important;
      }

      .p-dropdown-trigger {
        width: 28px !important;
        height: 26px !important;
        border-left: 1px solid #ced4da !important;
      }
    }

    // Force form label styling
    .form-label {
      margin-bottom: 4px !important;
      font-size: 0.875rem !important;
      font-weight: 500 !important;
      color: #495057 !important;
    }

    // Force compact spacing
    .mb-3 {
      margin-bottom: 0.75rem !important;
    }

    .mb-4 {
      margin-bottom: 1rem !important;
    }

    // Force error message styling
    .text-danger.small {
      font-size: 0.75rem !important;
      margin-top: 2px !important;
      line-height: 1.2 !important;
    }
  }
}

/* Add extra padding to the last card to ensure space at the bottom */
.card:last-of-type {
  margin-bottom: 60px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding-bottom: 80px; /* Extra padding on mobile */
  }
}

/* Fixed bottom buttons for mobile */
.fixed-bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* Toast message styling */
:host ::ng-deep {
  .p-toast {
    opacity: 1 !important;

    .p-toast-message {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

      &.p-toast-message-success {
        background-color: #e8f5e9;
        border-left: 6px solid #4caf50;
        color: #2e7d32;

        .p-toast-icon {
          color: #2e7d32;
        }

        .p-toast-summary {
          font-weight: 600;
        }

        .p-toast-detail {
          margin-top: 4px;
        }
      }

      &.p-toast-message-error {
        background-color: #ffebee;
        border-left: 6px solid #f44336;
        color: #c62828;

        .p-toast-icon {
          color: #c62828;
        }
      }
    }
  }
}
