<!-- Event Details Page Container -->
<div class="event-page-container">
  <!-- Navigation Header -->
  <div class="event-nav-header">
    <div class="container d-flex justify-content-between align-items-center">
      <a href="javascript:void(0)" class="back-button" (click)="goBack()">
        <i class="pi pi-arrow-left"></i>
        <span>Event Details</span>
      </a>
      <div class="d-flex">
        <div
          class="d-flex gap-2 me-2"
          *ngIf="
            isGlobalAdmin &&
            event?.statusName === 'Submitted' &&
            !isEventWithin30Minutes()
          "
        >
          <button pButton class="p-button-danger" (click)="rejectEvent()">
            <i class="pi pi-times me-1"></i> Reject
          </button>
          <button pButton class="p-button-success" (click)="approveEvent()">
            <i class="pi pi-check me-1"></i> Approve
          </button>
        </div>
        <div
          class="approval-expired-message"
          *ngIf="
            isGlobalAdmin &&
            event?.statusName === 'Submitted' &&
            isEventWithin30Minutes()
          "
        >
          <span class="p-badge p-badge-warning">
            <i class="pi pi-clock me-1"></i>
            Approval window closed (event starts in less than 30 minutes)
          </span>
        </div>
        <button
          pButton
          class="p-button-danger"
          (click)="editEvent()"
          *ngIf="
            isGlobalAdmin ||
            event?.organizerId === authService.getUserInfo()?.id
          "
          [disabled]="hasEventStarted()"
          [pTooltip]="
            hasEventStarted()
              ? 'Cannot edit event that has already started'
              : ''
          "
          tooltipPosition="top"
        >
          <i class="pi pi-pencil me-1"></i> Edit
        </button>
      </div>
    </div>
  </div>

  <!-- Toast for notifications -->
  <p-toast></p-toast>

  <!-- Rejection Dialog -->
  <p-dialog
    [(visible)]="showRejectDialog"
    [style]="{ width: '450px', minHeight: '300px' }"
    [modal]="true"
    [closable]="true"
    [closeOnEscape]="true"
    [draggable]="false"
    [resizable]="false"
    header="Reject Event"
    styleClass="event-reject-dialog"
    [baseZIndex]="10000"
    [autoZIndex]="true"
  >
    <div class="reject-dialog-content">
      <div class="form-group">
        <label for="rejectionReason"
          >Rejection Reason<span class="text-danger">*</span></label
        >
        <p-dropdown
          id="rejectionReason"
          [formControl]="rejectionReasonControl"
          [options]="rejectionReasons"
          placeholder="Select Reason"
          [style]="{ width: '100%' }"
          [required]="true"
          styleClass="w-full"
          optionLabel="label"
          optionValue="value"
          [showClear]="false"
          [filter]="false"
        ></p-dropdown>
        <div
          *ngIf="
            rejectionReasonControl.invalid && rejectionReasonControl.touched
          "
          class="text-danger small mt-1"
        >
          Rejection reason is required
        </div>
      </div>
      <div class="button-container">
        <button
          pButton
          type="button"
          label="Cancel"
          class="p-button-outlined p-button-secondary"
          (click)="onRejectDialogCancel()"
        ></button>
        <button
          pButton
          type="button"
          label="Reject"
          class="p-button-danger"
          [disabled]="rejectionReasonControl.invalid"
          (click)="onRejectDialogSubmit()"
        ></button>
      </div>
    </div>
  </p-dialog>

  <!-- Loading Spinner -->
  <div
    *ngIf="isLoading"
    class="d-flex justify-content-center align-items-center"
    style="min-height: 400px"
  >
    <p-progressSpinner
      strokeWidth="4"
      [style]="{ width: '50px', height: '50px' }"
    ></p-progressSpinner>
  </div>

  <!-- Error Message -->
  <div *ngIf="error && !isLoading" class="container mt-4">
    <div class="alert alert-danger">
      <div class="mb-2">{{ error }}</div>
      <button
        pButton
        class="p-button-sm p-button-danger"
        (click)="loadEventDetails()"
      >
        <i class="pi pi-refresh me-1"></i> Retry
      </button>
    </div>
  </div>

  <!-- Event Details Content -->
  <div *ngIf="!isLoading && !error && event" class="event-details-container">
    <!-- Main Image Banner -->
    <div class="cover-image">
      <img
        [src]="
          event.eventImageUrl
            ? getFullImagePath(event.eventImageUrl || '')
            : 'assets/images/placeholder.jpg'
        "
        alt="Event Banner"
        class="event-banner-img"
      />
    </div>

    <!-- Event Details Card -->
    <div class="content-container">
      <!-- Event Header Section -->
      <div class="content-section">
        <div class="event-title-container">
          <div class="event-title-wrapper">
            <h3 class="mb-0">{{ event.title }}</h3>
            <!-- Status Badge -->
            <span
              class="status-badge draft"
              *ngIf="event?.statusName === 'Draft'"
              >Draft</span
            >
            <span
              class="status-badge pending"
              *ngIf="event?.statusName === 'Submitted'"
              >Pending Review</span
            >
            <span
              class="status-badge approved"
              *ngIf="event?.statusName === 'Approved' && !hasEventStarted()"
              >Approved</span
            >
            <span
              class="status-badge event-started"
              *ngIf="event?.statusName === 'Approved' && hasEventStarted()"
              >Event Has Started</span
            >
            <span
              class="status-badge rejected"
              *ngIf="event?.statusName === 'Rejected'"
              >Rejected</span
            >
          </div>
        </div>
        <div class="event-meta">
          <span class="badge bg-light text-dark">{{
            formatEventCategory(event.category)
          }}</span>
          <span class="badge bg-primary text-white">{{
            formatEventType(event.typeName || "")
          }}</span>
        </div>
        <!-- Rejection Reason Section -->
        <div
          *ngIf="event?.statusName === 'Rejected' && event?.rejectionReason"
          class="mt-3"
        >
          <div class="rejection-reason">
            <div class="info-label">Rejection Reason:</div>
            <div class="info-value rejection-text">
              <i class="pi pi-times-circle"></i>
              {{ event.rejectionReason }}
            </div>
          </div>
        </div>
        <div class="mt-3">
          <p>{{ event.description }}</p>
        </div>
      </div>

      <!-- Submitter Information Section -->
      <div class="content-section">
        <div class="submitter-row">
          <div class="submitter-col">
            <div class="info-label">Submitted By</div>
            <div class="info-value">
              <i class="pi pi-user"></i>
              {{ event.organizerName || event.submitterName || "Unknown" }}
            </div>
          </div>
          <div class="submitter-col">
            <div class="info-label">Submitted On</div>
            <div class="info-value">
              <i class="pi pi-calendar"></i>
              {{ event.createdAt | date: "dd MMM yyyy" : "IST" }}
            </div>
          </div>
        </div>
      </div>

      <p-divider></p-divider>

      <!-- Event Schedule and Location Section -->
      <div class="content-section">
        <!-- Event Schedule -->
        <div class="schedule-row">
          <div class="schedule-col">
            <div class="info-label">Starts From</div>
            <div class="info-value">
              <i class="pi pi-calendar"></i>
              {{ event.eventStarts | date: "dd MMM yyyy" : "IST" }}
              {{
                event.startTime ? " - " + (event.startTime | slice: 0 : 5) : ""
              }}
            </div>
          </div>
          <div class="schedule-col">
            <div class="info-label">Ends On</div>
            <div class="info-value">
              <i class="pi pi-calendar"></i>
              {{ event.eventEnds | date: "dd MMM yyyy" : "IST" }}
              {{ event.endTime ? " - " + (event.endTime | slice: 0 : 5) : "" }}
            </div>
          </div>
        </div>

        <!-- Location -->
        <div class="location-row">
          <div class="info-label">Location</div>
          <div class="info-value">
            <!-- Online event info -->
            <ng-container
              *ngIf="event?.locationType === EventLocationType.Online"
            >
              <i class="pi pi-globe"></i>
              <span>Online Event :</span>
              <div *ngIf="event?.location?.meetingId" class="mt-2">
                <div class="online-meeting-details">
                  <div class="meeting-detail">
                    <strong>Meeting ID:</strong> {{ event.location.meetingId }}
                  </div>
                  <div *ngIf="event?.location?.passcode" class="meeting-detail">
                    <strong>Passcode:</strong> {{ event.location.passcode }}
                  </div>
                </div>
              </div>
            </ng-container>

            <!-- Physical address -->
            <ng-container
              *ngIf="
                event?.locationType === EventLocationType.Venue &&
                event?.location?.address1 &&
                event?.location?.address1 !== 'N/A' &&
                event?.location?.address1?.trim() !== ''
              "
            >
              <i class="pi pi-map-marker"></i>
              <span>
                {{ event.location.address1 }}
                {{
                  event.location.address2 &&
                  event.location.address2 !== "N/A" &&
                  event.location.address2.trim() !== ""
                    ? ", " + event.location.address2
                    : ""
                }}, {{ event.location.city }}, {{ event.location.state }}
                {{ event.location.zipCode }}
                {{
                  event.location.country ? ", " + event.location.country : ""
                }}
              </span>
            </ng-container>

            <!-- No location info available -->
            <ng-container
              *ngIf="
                (event?.locationType === EventLocationType.Venue &&
                  (!event?.location?.address1 ||
                    event?.location?.address1 === 'N/A' ||
                    event?.location?.address1?.trim() === '')) ||
                (event?.locationType === EventLocationType.Online &&
                  (!event?.location?.meetingId ||
                    event?.location?.meetingId === 'N/A' ||
                    event?.location?.meetingId?.trim() === ''))
              "
            >
              <i class="pi pi-exclamation-triangle"></i>
              <span>No location information available</span>
            </ng-container>
          </div>
        </div>
      </div>

      <p-divider *ngIf="event?.contactDetails"></p-divider>

      <!-- Contact Details Section -->
      <div class="content-section" *ngIf="event?.contactDetails">
        <div class="contact-row">
          <div class="contact-col" *ngIf="event?.contactDetails?.contactName">
            <div class="info-label">Contact Name</div>
            <div class="info-value">
              <i class="pi pi-user"></i>
              {{ event.contactDetails.contactName }}
            </div>
          </div>
          <div class="contact-col" *ngIf="event?.contactDetails?.contactNo">
            <div class="info-label">Contact No</div>
            <div class="info-value">
              <i class="pi pi-phone"></i>
              {{ event.contactDetails.contactNo }}
            </div>
          </div>
          <div class="contact-col" *ngIf="event?.contactDetails?.email">
            <div class="info-label">Email</div>
            <div class="info-value">
              <i class="pi pi-envelope"></i>
              <a
                href="mailto:{{ event.contactDetails.email }}"
                class="text-decoration-none"
              >
                {{ event.contactDetails.email }}
              </a>
            </div>
          </div>
          <div class="contact-col" *ngIf="event?.contactDetails?.website">
            <div class="info-label">Website</div>
            <div class="info-value">
              <i class="pi pi-globe"></i>
              <a
                [href]="event.contactDetails.website"
                target="_blank"
                class="text-decoration-none"
              >
                {{ event.contactDetails.website }}
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Review Information Section -->
      <div
        class="content-section"
        *ngIf="
          event?.statusName === 'Approved' || event?.statusName === 'Rejected'
        "
      >
        <div class="review-row">
          <div class="review-col">
            <div class="info-label">Reviewed By</div>
            <div class="info-value">
              <i class="pi pi-user"></i>
              {{ event.reviewedByName || "Admin" }}
            </div>
          </div>
          <div class="review-col" *ngIf="event?.reviewedOn">
            <div class="info-label">Reviewed On</div>
            <div class="info-value">
              <i class="pi pi-calendar"></i>
              {{ event.reviewedOn | date: "dd MMM yyyy" : "IST" }}
            </div>
          </div>
        </div>

        <!-- Rejection Reason Section -->
        <!-- <div
          *ngIf="event?.statusName === 'Rejected' && event?.rejectionReason"
          class="mt-3"
        >
          <div class="rejection-reason">
            <div class="info-label">Rejection Reason:</div>
            <div class="info-value rejection-text">
              <i class="pi pi-times-circle"></i>
              {{ event.rejectionReason }}
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>
